package routes

import (
	"carnow-backend/internal/config"
	"carnow-backend/internal/handlers"
	"carnow-backend/internal/services"
	"carnow-backend/internal/shared/middleware"

	"github.com/gin-gonic/gin"
)

// SetupSimpleAuthRoutes sets up authentication routes using Supabase JWT only
// Forever Plan Compliant: Simple architecture with Supabase Auth
func SetupSimpleAuthRoutes(r *gin.Engine, cfg *config.Config, supabaseAuth *services.SupabaseAuthService) {
	// Create simple auth handlers
	authHandlers := handlers.NewSimpleAuthHandlers(supabaseAuth, cfg)

	// Create Supabase JWT middleware
	authMiddleware := middleware.SupabaseJWTMiddleware(cfg, supabaseAuth)

	// Auth routes group
	auth := r.Group("/api/v1/auth")
	{
		// Public routes (no authentication required)
		auth.POST("/login", authHandlers.Login)
		auth.POST("/register", authHandlers.Register)
		auth.POST("/google", authHandlers.GoogleAuth)
		auth.POST("/refresh", authHandlers.RefreshToken)

		// Protected routes (authentication required)
		protected := auth.Group("")
		protected.Use(authMiddleware)
		{
			protected.POST("/logout", authHandlers.Logout)
			protected.GET("/profile", authHandlers.GetProfile)
		}
	}
}
