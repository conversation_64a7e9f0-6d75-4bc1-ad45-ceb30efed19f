// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'google_auth_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$googleAuthServiceHash() => r'e9eda0b0e1b780a350f8662b3cbdcdfa4e71b182';

/// Provider for GoogleAuthService
/// موفر خدمة مصادقة Google
///
/// Copied from [googleAuthService].
@ProviderFor(googleAuthService)
final googleAuthServiceProvider =
    AutoDisposeProvider<GoogleAuthService>.internal(
      googleAuthService,
      name: r'googleAuthServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$googleAuthServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GoogleAuthServiceRef = AutoDisposeProviderRef<GoogleAuthService>;
String _$currentGoogleUserHash() => r'7d59ac5c6a4d7f2ef0e5c7c63117448006b16713';

/// Provider for current Google user
/// موفر المستخدم الحالي لـ Google
///
/// Copied from [currentGoogleUser].
@ProviderFor(currentGoogleUser)
final currentGoogleUserProvider = AutoDisposeProvider<GoogleUserInfo?>.internal(
  currentGoogleUser,
  name: r'currentGoogleUserProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentGoogleUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentGoogleUserRef = AutoDisposeProviderRef<GoogleUserInfo?>;
String _$isGoogleAuthenticatedHash() =>
    r'6761b4eabac6b6fd8b140b492ff0272bb81e41d2';

/// Provider for Google authentication state
/// موفر حالة مصادقة Google
///
/// Copied from [isGoogleAuthenticated].
@ProviderFor(isGoogleAuthenticated)
final isGoogleAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isGoogleAuthenticated,
  name: r'isGoogleAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isGoogleAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsGoogleAuthenticatedRef = AutoDisposeProviderRef<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
