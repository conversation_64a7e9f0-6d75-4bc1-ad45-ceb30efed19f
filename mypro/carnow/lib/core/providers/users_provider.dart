// =============================================================================
// Users Provider - Forever Plan Architecture
// مزود المستخدمين - بنية الخطة الدائمة
// =============================================================================
// Flutter (UI Only) → Go API → Supabase (Data Only)

import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/account/models/user_model.dart';
import '../providers/dio_provider.dart';
import '../auth/simple_supabase_auth_provider.dart';
import '../extensions/user_seller_extension.dart';

part 'users_provider.g.dart';

final _logger = Logger();

/// AsyncNotifier for users management using Forever Plan Architecture
@riverpod
class UsersNotifier extends _$UsersNotifier {
  @override
  FutureOr<List<UserModel>> build() async {
    try {
      final dio = ref.read(dioProvider);
      _logger.i('Fetching users from Go backend');

      // This endpoint should be protected by middleware on the backend
      final response = await dio.get('/api/v1/users');
      final data = response.data as List;

      final users = data
          .map<UserModel>((json) => UserModel.fromJson(json))
          .toList();
      _logger.i('Successfully fetched ${users.length} users');

      return users;
    } on DioException catch (e, stackTrace) {
      _logger.e(
        'Error fetching users from backend',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    } catch (e, stackTrace) {
      _logger.e(
        'Generic error fetching users',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}

// =============================================================================
// Current User Stream - تيار المستخدم الحالي
// =============================================================================

/// Provides a stream of the current user's profile information.
///
/// This provider is the ONLY source for current user data.
/// It uses SimpleAuthSystem as the single source of truth.
@riverpod
Stream<UserModel?> currentUserStream(Ref ref) async* {
  // Keep this critical provider alive
  ref.keepAlive();

  // Watch authentication state directly to get real-time updates
  final authState = ref.watch(simpleSupabaseAuthProvider);

  // Handle different auth states
  if (authState is! SimpleAuthStateAuthenticated) {
    _logger.i('No authenticated user, yielding null');
    yield null;
    return;
  }

  final currentUser = authState.user;
  _logger.i('Authenticated user found: ${currentUser.email}');

  try {
    // Fetch complete user data from Go backend API
    final dio = ref.read(dioProvider);
    _logger.i('Fetching complete user profile from Go backend for: ${currentUser.email}');
    
    try {
      // Call Go backend API to get complete user profile with seller information
      final response = await dio.get('/api/v1/user/profile');
      
      if (response.statusCode == 200 && response.data != null) {
        // Parse complete UserModel from backend response
        final userModel = UserModel.fromJson(response.data);
        _logger.i('✅ Complete user data fetched: ${userModel.name} (${userModel.email}) - Seller: ${userModel.isSeller}');
        yield userModel;
        return;
      }
    } catch (apiError) {
      _logger.w('⚠️ Failed to fetch from backend API: $apiError');
      
      // Check if it's an authentication error
      if (apiError.toString().contains('UNAUTHORIZED') || 
          apiError.toString().contains('401') ||
          apiError.toString().contains('Token validation failed')) {
        _logger.w('⚠️ Authentication error detected - using fallback user model without clearing auth state');
        // Don't clear auth state for authentication errors - just use fallback
      } else {
        _logger.w('⚠️ Non-authentication error - using fallback user model');
      }
      // Fall back to basic user model if API fails
    }
    
    // Fallback: Create basic UserModel from auth data if API fails
    _logger.i('📋 Using fallback user model from auth data');
    final user = currentUser;
    final userModel = UserModel(
      id: user.id,
      authId: user.id, // Required: Use user.id for authId
      email: user.email,
      name: user.email?.split('@').first ?? 'User',
      phone: null, // Will be fetched from backend API
      profileImageUrl: null, // Will be fetched from backend API
      createdAt: DateTime.now(), // Will be fetched from backend API
      updatedAt: DateTime.now(),
      // Default seller values - will be false unless fetched from backend
      isApproved: false,
      isSellerRequested: false,
    );

    _logger.i('Yielding fallback user: ${userModel.name} (${userModel.email})');
    yield userModel;
  } catch (e) {
    _logger.e('Error in currentUserStream: $e');
    yield null;
  }
}

// =============================================================================
// DEPRECATED: Legacy Compatibility - التوافق العكسي
// =============================================================================

/// DEPRECATED: Use usersNotifierProvider instead
@Deprecated('Use usersNotifierProvider instead')
final usersProvider = Provider.autoDispose<List<UserModel>>((ref) {
  final asyncValue = ref.watch(usersNotifierProvider);
  return asyncValue.asData?.value ?? [];
});

// =============================================================================
// REMOVED: Conflicting Providers
// =============================================================================
// The following providers have been REMOVED to eliminate conflicts:
// - currentUserProvider (now in unified_auth_provider.dart)
// - Multiple auth state providers
// 
// Use ONLY these providers from unified_auth_provider.dart:
// - unifiedAuthProviderProvider - Main auth system
// - isAuthenticatedProvider - Auth status check
// - currentUserProvider - Current Supabase User
// - authErrorProvider - Auth errors
// 
// For UserModel stream, use:
// - currentUserStreamProvider - Current user as UserModel
// =============================================================================
