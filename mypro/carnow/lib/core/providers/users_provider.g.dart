// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'users_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentUserStreamHash() => r'b72848e7d05fc53f70d1eea35c84d75271c563cf';

/// Provides a stream of the current user's profile information.
///
/// This provider is the ONLY source for current user data.
/// It uses SimpleAuthSystem as the single source of truth.
///
/// Copied from [currentUserStream].
@ProviderFor(currentUserStream)
final currentUserStreamProvider =
    AutoDisposeStreamProvider<UserModel?>.internal(
      currentUserStream,
      name: r'currentUserStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentUserStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserStreamRef = AutoDisposeStreamProviderRef<UserModel?>;
String _$usersNotifierHash() => r'dc45ffba1a4f6084cb4e9cf0a2a7b98136ad4bdc';

/// AsyncNotifier for users management using Forever Plan Architecture
///
/// Copied from [UsersNotifier].
@ProviderFor(UsersNotifier)
final usersNotifierProvider =
    AutoDisposeAsyncNotifierProvider<UsersNotifier, List<UserModel>>.internal(
      UsersNotifier.new,
      name: r'usersNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$usersNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UsersNotifier = AutoDisposeAsyncNotifier<List<UserModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
