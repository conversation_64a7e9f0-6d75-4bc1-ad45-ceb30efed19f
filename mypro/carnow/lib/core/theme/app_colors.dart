import 'package:flutter/material.dart';
import 'carnow_material3_colors.dart';

/// CarNow App Colors - Forever Plan Compliant Material 3 System
///
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data + Auth)
/// - Uses Material 3 color system exclusively
/// - No hardcoded colors in production code
/// - Accessibility compliant (WCAG AAA)
/// - Real data only from Supabase database
///
/// @deprecated Use CarNowMaterial3Colors and Theme.of(context).colorScheme instead
/// This file is kept for backward compatibility only
class AppColors {
  AppColors._(); // Private constructor to prevent instantiation

  // =============================================================================
  // MATERIAL 3 SEED COLORS - Forever Plan Compliant
  // =============================================================================

  /// Primary brand color - CarNow Forest Green (Material 3 seed)
  /// @deprecated Use Theme.of(context).colorScheme.primary instead
  static const primary = CarNowMaterial3Colors.primarySeed;

  /// Secondary brand color - CarNow Sky Blue (Material 3 seed)
  /// @deprecated Use Theme.of(context).colorScheme.secondary instead
  static const secondary = CarNowMaterial3Colors.secondarySeed;

  /// Tertiary brand color - CarNow Orange (Material 3 seed)
  /// @deprecated Use Theme.of(context).colorScheme.tertiary instead
  static const tertiary = CarNowMaterial3Colors.tertiarySeed;

  // =============================================================================
  // BASIC COLORS - Forever Plan Compliant
  // =============================================================================

  /// @deprecated Use Theme.of(context).colorScheme.surface instead
  static const white = Colors.white;

  /// @deprecated Use Theme.of(context).colorScheme.onSurface instead
  static const black = Colors.black;

  // =============================================================================
  // BACKWARD COMPATIBILITY - Forever Plan Compliant
  // =============================================================================

  /// @deprecated Use Theme.of(context).colorScheme.primaryContainer instead
  static const primaryLight = Color(0xFF4CAF50);

  /// @deprecated Use Theme.of(context).colorScheme.primary instead
  static const primaryDark = primary;

  /// @deprecated Use Theme.of(context).colorScheme.secondaryContainer instead
  static const secondaryLight = Color(0xFF64B5F6);

  /// @deprecated Use Theme.of(context).colorScheme.secondary instead
  static const secondaryDark = secondary;

  /// @deprecated Use Theme.of(context).colorScheme.tertiaryContainer instead
  static const tertiaryLight = Color(0xFFFFB74D);

  /// @deprecated Use Theme.of(context).colorScheme.tertiary instead
  static const tertiaryDark = tertiary;

  /// @deprecated Use Theme.of(context).colorScheme.outline instead
  static const grey = Color(0xFF8A94A6);

  /// @deprecated Use Theme.of(context).colorScheme.error instead
  static const success = tertiary;

  /// @deprecated Use Theme.of(context).colorScheme.secondary instead
  static const warning = secondary;

  /// @deprecated Use Theme.of(context).colorScheme.error instead
  static const error = primary;

  /// @deprecated Use Theme.of(context).colorScheme.primary instead
  static const info = primary;

  /// @deprecated Use Theme.of(context).colorScheme.surface instead
  static const background = Color(0xFFF8F9FA);

  /// @deprecated Use Theme.of(context).colorScheme.surface instead
  static const backgroundLight = Color(0xFFFCFDFE);

  /// @deprecated Use Theme.of(context).colorScheme.surface instead
  static const surface = Colors.white;

  /// @deprecated Use Theme.of(context).colorScheme.surface instead
  static const card = Colors.white;

  /// @deprecated Use Theme.of(context).colorScheme.outline instead
  static const border = Color(0xFFE1E4E8);

  /// @deprecated Use Theme.of(context).colorScheme.outline instead
  static const borderLight = Color(0xFFEFF1F3);

  /// @deprecated Use Theme.of(context).colorScheme.outline instead
  static const borderDark = Color(0xFFCED3D9);

  // Auction-specific (aligned with the new palette)
  static const auctionActive = Color(0xFF86B817); // tertiary green
  static const auctionEnding = Color(0xFFF5D400); // secondary yellow
  static const auctionEnded = Color(0xFF8A94A6); // grey
  static const bidWinning = Color(0xFFE53238); // primary red
  static const bidLosing = Color(0xFFC2A200); // secondary dark

  /// Map auction status string → color helper
  static Color auctionStatus(String status) => switch (status.toLowerCase()) {
    'active' => auctionActive,
    'ending' => auctionEnding,
    'ended' => auctionEnded,
    _ => grey,
  };

  /// Map bid outcome → color helper
  static Color bidStatus({required bool isWinning}) =>
      isWinning ? bidWinning : bidLosing;

  // Text colors
  static const textPrimary = Color(0xFF1A1A1A); // High-emphasis text
  static const textSecondary = Color(0xFF5A5A5A); // Medium-emphasis text
  static const textHint = Color(0xFF8A94A6); // Hints / placeholders
  static const textOnPrimary =
      Colors.white; // Text on primary colored backgrounds

  // Commerce-specific colors
  /// Price and discount values
  static const price = Color(0xFFE53238); // primary red for attention

  static const metallic = Color(0xFF78909C);
  static const premium = Color(0xFF37474F);
  static const performance = Color(0xFFD32F2F);
  static const efficiency = Color(0xFF388E3C);

  static const successContainer = Color(0xFFC8E6C9);
  static const warningContainer = Color(0xFFFFE0B2);
  static const errorContainer = Color(0xFFFFCDD2);
}
