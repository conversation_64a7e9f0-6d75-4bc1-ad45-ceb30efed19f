// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isAuthenticatedHash() => r'0ae0478618b7956f216187165c8b582de3f02355';

/// Provider for authentication status (compatibility)
///
/// Copied from [isAuthenticated].
@ProviderFor(isAuthenticated)
final isAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isAuthenticated,
  name: r'isAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$nativeGoogleAuthNotifierHash() =>
    r'2e79c43db9a27f15fb5006bc450d46fa2d8e5a9b';

/// See also [NativeGoogleAuthNotifier].
@ProviderFor(NativeGoogleAuthNotifier)
final nativeGoogleAuthNotifierProvider =
    AutoDisposeStreamNotifierProvider<
      NativeGoogleAuthNotifier,
      UserModel?
    >.internal(
      NativeGoogleAuthNotifier.new,
      name: r'nativeGoogleAuthNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$nativeGoogleAuthNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NativeGoogleAuthNotifier = AutoDisposeStreamNotifier<UserModel?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
