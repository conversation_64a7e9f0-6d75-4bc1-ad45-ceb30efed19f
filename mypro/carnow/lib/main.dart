// =============================================================================
// CarNow Application - Forever Plan Compliant Entry Point
// =============================================================================
//
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data + Auth)
// Simple, clean, and production-ready main.dart following Forever Plan principles
//
// Core Features:
// - Simple Supabase authentication via Go backend
// - Material 3 design system
// - Real data only from Supabase database
// - No complex initialization or enhanced services
//

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import 'core/app/carnow_app.dart';
import 'core/app/simple_app_initialization.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'core/auth/google_auth_service.dart';
import 'core/auth/google_oauth_config.dart';

/// CarNow main function - Forever Plan Compliant
/// Simple initialization following Forever Plan principles
Future<void> main() async {
  // Basic Flutter initialization
  WidgetsFlutterBinding.ensureInitialized();

  // Set up basic logging
  Logger.root.level = kDebugMode ? Level.ALL : Level.INFO;

  // Load environment variables for Google OAuth
  try {
    await dotenv.load();
  } catch (_) {
    if (kDebugMode) {
      print('Environment not loaded, using default configuration');
    }
  }

  // Simple app initialization - Forever Plan Compliant
  try {
    await SimpleAppInitialization.initialize();
  } catch (e, stackTrace) {
    Logger.root.severe('Failed to initialize app', e, stackTrace);
  }

  // Initialize Google Auth Service
  try {
    final googleAuthService = GoogleAuthService.instance;
    await googleAuthService.initialize(
      clientId: GoogleOAuthConfig.getClientId(),
      scopes: ['email', 'profile'],
    );
    if (kDebugMode) {
      print('✅ Google Auth Service initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('⚠️ Google Auth Service initialization failed: $e');
    }
  }

  // Run the app - Forever Plan Compliant
  runApp(
    const ProviderScope(
      child: CarNowApp(),
    ),
  );
}
